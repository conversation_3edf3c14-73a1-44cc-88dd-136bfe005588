
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from 'react-router-dom'
import { useEffect } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import ScrollToTop from '@/components/ScrollToTop'
import BottomNavigation from '@/components/BottomNavigation'
import SEOHead from '@/components/SEOHead'
import StructuredData from '@/components/StructuredData'
import { AuthProvider } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/ProtectedRoute'
import Index from './pages/Index'
import Services from './pages/Services'
import Work from './pages/Work'
import About from './pages/About'
import Process from './pages/Process'
import Contact from './pages/Contact'
import Pricing from './pages/Pricing'
import EventLanding from './pages/EventLanding'
import CodeAndAIWorkshop from './pages/CodeAndAIWorkshop'
import TechMeetup from './pages/TechMeetup'
import Dashboard from './pages/Dashboard'
import Login from './pages/Login'
import NotFound from './pages/NotFound'
import ThankYou from './pages/ThankYou'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger)

const queryClient = new QueryClient()

// Component to conditionally render BottomNavigation
const ConditionalBottomNavigation = () => {
  const location = useLocation()

  // Hide bottom navigation on landing pages and dashboard
  const hiddenRoutes = [
    '/event',
    '/code-and-ai-workshop',
    '/training',
    '/tech-meetup',
    '/meetup',
    '/dashboard'
  ]
  const shouldHide =
    hiddenRoutes.includes(location.pathname) ||
    location.pathname.startsWith('/dashboard')

  if (shouldHide) return null

  return <BottomNavigation />
}

const App = () => {
  useEffect(() => {
    // Set up GSAP defaults if needed
    gsap.config({
      nullTargetWarn: false
    })

    // Clean up on unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
            <SEOHead />
            <StructuredData />
            <ScrollToTop />
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/services" element={<Services />} />
              <Route path="/work" element={<Work />} />
              <Route path="/about" element={<About />} />
              <Route path="/process" element={<Process />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/event" element={<EventLanding />} />
              <Route
                path="/code-and-ai-workshop"
                element={<CodeAndAIWorkshop />}
              />
              <Route path="/training" element={<CodeAndAIWorkshop />} />
              <Route path="/tech-meetup" element={<TechMeetup />} />
              <Route path="/meetup" element={<TechMeetup />} />
              <Route path="/thank-you" element={<ThankYou />} />
              <Route path="/login" element={<Login />} />
              {/* Dashboard Routes - Protected */}
              <Route
                path="/dashboard/*"
                element={
                  <ProtectedRoute requirePayment={true}>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            <ConditionalBottomNavigation />
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  )
}

export default App;
