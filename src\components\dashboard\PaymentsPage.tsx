import React, { useEffect } from 'react'
import { gsap } from 'gsap'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { usePayments } from '@/hooks/useDashboard'
import { 
  CreditCard, 
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  Loader2,
  TrendingUp,
  Receipt
} from 'lucide-react'

const PaymentsPage: React.FC = () => {
  const { data: paymentSummary, isLoading, error } = usePayments()

  useEffect(() => {
    if (!isLoading && paymentSummary) {
      gsap.fromTo('.payment-card',
        { y: 30, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 0.6, 
          stagger: 0.1,
          ease: 'power2.out'
        }
      )
    }
  }, [isLoading, paymentSummary])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <CreditCard className="h-4 w-4 text-agency-white-muted" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      success: { label: 'Paid', className: 'bg-green-500/10 text-green-500 border-green-500/20' },
      pending: { label: 'Pending', className: 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20' },
      failed: { label: 'Failed', className: 'bg-red-500/10 text-red-500 border-red-500/20' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return <Badge className={config.className}>{config.label}</Badge>
  }

  const formatCurrency = (amount: number, currency: string = '₦') => {
    return `${currency}${amount.toLocaleString()}`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-agency-green mx-auto mb-4" />
          <p className="text-agency-white-muted">Loading your payment history...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">Failed to load payment information</p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Payments</h1>
          <p className="text-agency-white-muted mt-1">
            View your payment history, invoices, and billing information
          </p>
        </div>
        <Button className="bg-agency-green hover:bg-agency-green/90 text-agency-dark">
          <Download className="mr-2 h-4 w-4" />
          Download Receipt
        </Button>
      </div>

      {/* Payment Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="payment-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-agency-white-muted">
              Total Paid
            </CardTitle>
            <div className="p-2 rounded-lg bg-green-500/10">
              <TrendingUp className="h-4 w-4 text-green-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {formatCurrency(paymentSummary?.totalPaid || 0)}
            </div>
            <p className="text-xs text-agency-white-muted">
              All time payments
            </p>
          </CardContent>
        </Card>

        <Card className="payment-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-agency-white-muted">
              Pending
            </CardTitle>
            <div className="p-2 rounded-lg bg-yellow-500/10">
              <Clock className="h-4 w-4 text-yellow-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {formatCurrency(paymentSummary?.totalPending || 0)}
            </div>
            <p className="text-xs text-agency-white-muted">
              Awaiting processing
            </p>
          </CardContent>
        </Card>

        <Card className="payment-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-agency-white-muted">
              Status
            </CardTitle>
            <div className="p-2 rounded-lg bg-agency-green/10">
              <CheckCircle className="h-4 w-4 text-agency-green" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {paymentSummary?.subscriptionStatus === 'active' ? 'Active' : 'Inactive'}
            </div>
            <p className="text-xs text-agency-white-muted">
              Account status
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Payment History */}
      {paymentSummary?.paymentHistory && paymentSummary.paymentHistory.length > 0 ? (
        <Card className="payment-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Receipt className="h-5 w-5 text-agency-green" />
              Payment History
            </CardTitle>
            <CardDescription className="text-agency-white-muted">
              Your complete payment transaction history
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {paymentSummary.paymentHistory.map((payment) => (
                <div 
                  key={payment.id} 
                  className="flex items-center justify-between p-4 bg-agency-dark/50 rounded-lg hover:bg-agency-dark/70 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    {getStatusIcon(payment.status)}
                    <div>
                      <h4 className="text-white font-medium">
                        {formatCurrency(payment.amount, payment.currency)}
                      </h4>
                      <div className="flex items-center gap-4 text-sm text-agency-white-muted">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(payment.payment_date).toLocaleDateString()}
                        </div>
                        <span>{payment.payment_method}</span>
                        <span>Ref: {payment.paystack_reference}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {getStatusBadge(payment.status)}
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-agency-green hover:bg-agency-green/10"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="bg-agency-darker/50 border-agency-green/10">
          <CardContent className="text-center py-12">
            <CreditCard className="h-12 w-12 text-agency-white-muted mx-auto mb-4" />
            <h3 className="text-white text-lg font-medium mb-2">No Payment History</h3>
            <p className="text-agency-white-muted mb-6">
              Your payment transactions will appear here once you make your first payment.
            </p>
            <Button className="bg-agency-green hover:bg-agency-green/90 text-agency-dark">
              Make Payment
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Payment Methods & Help */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="payment-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader>
            <CardTitle className="text-white">Payment Methods</CardTitle>
            <CardDescription className="text-agency-white-muted">
              We accept various payment methods
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-3 p-2 bg-agency-dark/30 rounded">
              <CreditCard className="h-5 w-5 text-agency-green" />
              <span className="text-white">Credit/Debit Cards</span>
            </div>
            <div className="flex items-center gap-3 p-2 bg-agency-dark/30 rounded">
              <CreditCard className="h-5 w-5 text-agency-green" />
              <span className="text-white">Bank Transfer</span>
            </div>
            <div className="flex items-center gap-3 p-2 bg-agency-dark/30 rounded">
              <CreditCard className="h-5 w-5 text-agency-green" />
              <span className="text-white">Mobile Money</span>
            </div>
          </CardContent>
        </Card>

        <Card className="payment-card bg-agency-darker/50 border-agency-green/10">
          <CardHeader>
            <CardTitle className="text-white">Need Help?</CardTitle>
            <CardDescription className="text-agency-white-muted">
              Payment support and assistance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start">
              <Receipt className="mr-2 h-4 w-4" />
              Request Invoice
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Download className="mr-2 h-4 w-4" />
              Download Receipt
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <AlertCircle className="mr-2 h-4 w-4" />
              Report Issue
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default PaymentsPage
