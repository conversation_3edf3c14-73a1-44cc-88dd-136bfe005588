import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@/contexts/AuthContext'
import { dbHelpers } from '@/lib/supabase'
import { getMockDashboardData } from '@/data/dashboard/mockData'
import {
  DashboardData,
  DashboardStats,
  ProjectWithMilestones,
  MessageThread,
  DocumentCategory,
  PaymentSummary,
  ActivityItem
} from '@/types/dashboard'
import { toast } from '@/components/ui/sonner'

// Check if we should use mock data
const useMockData =
  !import.meta.env.VITE_SUPABASE_URL ||
  import.meta.env.VITE_ENABLE_MOCK_DATA === 'true'

// Query keys
export const DASHBOARD_QUERY_KEYS = {
  dashboard: (userId: string) => ['dashboard', userId],
  projects: (userId: string) => ['projects', userId],
  messages: (userId: string) => ['messages', userId],
  documents: (userId: string) => ['documents', userId],
  payments: (userId: string) => ['payments', userId],
  stats: (userId: string) => ['stats', userId],
  activity: (userId: string) => ['activity', userId]
} as const

// Main dashboard hook
export const useDashboard = () => {
  const { user } = useAuth()

  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.dashboard(user?.id || ''),
    queryFn: async (): Promise<DashboardData> => {
      if (!user) throw new Error('User not authenticated')

      // Fetch all dashboard data in parallel
      const [projects, messages, documents, payments] = await Promise.all([
        fetchUserProjects(user.id),
        fetchUserMessages(user.id),
        fetchUserDocuments(user.id),
        fetchUserPayments(user.id)
      ])

      // Calculate stats
      const stats = calculateDashboardStats(
        projects,
        messages,
        documents,
        payments
      )

      return {
        user,
        stats,
        projects,
        messages,
        documents,
        payments
      }
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  })
}

// Individual data hooks
export const useProjects = () => {
  const { user } = useAuth()

  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.projects(user?.id || ''),
    queryFn: () => fetchUserProjects(user!.id),
    enabled: !!user,
    staleTime: 2 * 60 * 1000 // 2 minutes
  })
}

export const useMessages = () => {
  const { user } = useAuth()

  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.messages(user?.id || ''),
    queryFn: () => fetchUserMessages(user!.id),
    enabled: !!user,
    staleTime: 30 * 1000 // 30 seconds
  })
}

export const useDocuments = () => {
  const { user } = useAuth()

  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.documents(user?.id || ''),
    queryFn: () => fetchUserDocuments(user!.id),
    enabled: !!user,
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}

export const usePayments = () => {
  const { user } = useAuth()

  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.payments(user?.id || ''),
    queryFn: () => fetchUserPayments(user!.id),
    enabled: !!user,
    staleTime: 10 * 60 * 1000 // 10 minutes
  })
}

// Mutation hooks
export const useSendMessage = () => {
  const queryClient = useQueryClient()
  const { user } = useAuth()

  return useMutation({
    mutationFn: async ({
      subject,
      content,
      projectId
    }: {
      subject: string
      content: string
      projectId?: string
    }) => {
      if (!user) throw new Error('User not authenticated')

      // This would integrate with your messaging system
      // For now, we'll simulate the API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return { success: true }
    },
    onSuccess: () => {
      toast.success('Message sent successfully!')
      queryClient.invalidateQueries({
        queryKey: DASHBOARD_QUERY_KEYS.messages(user?.id || '')
      })
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send message')
    }
  })
}

// Helper functions
async function fetchUserProjects(
  userId: string
): Promise<ProjectWithMilestones[]> {
  if (useMockData) {
    const mockData = getMockDashboardData()
    return mockData.projects.map((project) => ({
      ...project,
      milestones: project.project_milestones || [],
      nextMilestone: project.project_milestones?.find(
        (m) => m.status === 'pending'
      ),
      completedMilestones:
        project.project_milestones?.filter((m) => m.status === 'completed')
          .length || 0,
      totalMilestones: project.project_milestones?.length || 0
    }))
  }

  try {
    const projects = await dbHelpers.getUserProjects(userId)

    return projects.map((project) => ({
      ...project,
      milestones: project.project_milestones || [],
      nextMilestone: project.project_milestones?.find(
        (m) => m.status === 'pending'
      ),
      completedMilestones:
        project.project_milestones?.filter((m) => m.status === 'completed')
          .length || 0,
      totalMilestones: project.project_milestones?.length || 0
    }))
  } catch (error) {
    console.error('Error fetching projects:', error)
    return []
  }
}

async function fetchUserMessages(userId: string): Promise<MessageThread[]> {
  if (useMockData) {
    const mockData = getMockDashboardData()
    const messages = mockData.messages

    // Group messages by subject/thread
    const threads: { [key: string]: MessageThread } = {}

    messages.forEach((message) => {
      const threadKey = message.subject
      if (!threads[threadKey]) {
        threads[threadKey] = {
          id: threadKey,
          subject: message.subject,
          lastMessage: message,
          messageCount: 1,
          unreadCount: message.is_read ? 0 : 1,
          participants: [message.sender_name],
          projectId: message.project_id
        }
      } else {
        threads[threadKey].messageCount++
        if (!message.is_read) threads[threadKey].unreadCount++
        if (
          new Date(message.created_at) >
          new Date(threads[threadKey].lastMessage.created_at)
        ) {
          threads[threadKey].lastMessage = message
        }
      }
    })

    return Object.values(threads)
  }

  try {
    const messages = await dbHelpers.getUserMessages(userId)

    // Group messages by subject/thread
    const threads: { [key: string]: MessageThread } = {}

    messages.forEach((message) => {
      const threadKey = message.subject
      if (!threads[threadKey]) {
        threads[threadKey] = {
          id: threadKey,
          subject: message.subject,
          lastMessage: message,
          messageCount: 1,
          unreadCount: message.is_read ? 0 : 1,
          participants: [message.sender_name],
          projectId: message.project_id
        }
      } else {
        threads[threadKey].messageCount++
        if (!message.is_read) threads[threadKey].unreadCount++
        if (
          new Date(message.created_at) >
          new Date(threads[threadKey].lastMessage.created_at)
        ) {
          threads[threadKey].lastMessage = message
        }
      }
    })

    return Object.values(threads)
  } catch (error) {
    console.error('Error fetching messages:', error)
    return []
  }
}

async function fetchUserDocuments(userId: string): Promise<DocumentCategory[]> {
  try {
    const documents = await dbHelpers.getUserDocuments(userId)

    const categories: { [key: string]: DocumentCategory } = {
      contract: {
        type: 'contract',
        label: 'Contracts',
        count: 0,
        documents: []
      },
      invoice: { type: 'invoice', label: 'Invoices', count: 0, documents: [] },
      deliverable: {
        type: 'deliverable',
        label: 'Deliverables',
        count: 0,
        documents: []
      },
      other: { type: 'other', label: 'Other', count: 0, documents: [] }
    }

    documents.forEach((doc) => {
      categories[doc.type].documents.push(doc)
      categories[doc.type].count++
    })

    return Object.values(categories)
  } catch (error) {
    console.error('Error fetching documents:', error)
    return []
  }
}

async function fetchUserPayments(userId: string): Promise<PaymentSummary> {
  try {
    const payments = await dbHelpers.getUserPayments(userId)

    const totalPaid = payments
      .filter((p) => p.status === 'success')
      .reduce((sum, p) => sum + p.amount, 0)

    const totalPending = payments
      .filter((p) => p.status === 'pending')
      .reduce((sum, p) => sum + p.amount, 0)

    const lastPayment = payments.find((p) => p.status === 'success')

    return {
      totalPaid,
      totalPending,
      lastPayment,
      paymentHistory: payments,
      subscriptionStatus: totalPaid > 0 ? 'active' : undefined
    }
  } catch (error) {
    console.error('Error fetching payments:', error)
    return {
      totalPaid: 0,
      totalPending: 0,
      paymentHistory: []
    }
  }
}

function calculateDashboardStats(
  projects: ProjectWithMilestones[],
  messages: MessageThread[],
  documents: DocumentCategory[],
  payments: PaymentSummary
): DashboardStats {
  return {
    totalProjects: projects.length,
    activeProjects: projects.filter((p) => p.status === 'in_progress').length,
    completedProjects: projects.filter((p) => p.status === 'completed').length,
    totalSpent: payments.totalPaid,
    pendingMessages: messages.reduce(
      (sum, thread) => sum + thread.unreadCount,
      0
    ),
    recentDocuments: documents.reduce((sum, cat) => sum + cat.count, 0)
  }
}
