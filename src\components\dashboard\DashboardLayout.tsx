import React, { useState, useEffect } from 'react'
import { Outlet, useLocation } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { cn } from '@/lib/utils'
import { gsap } from 'gsap'
import DashboardSidebar from './DashboardSidebar'
import DashboardHeader from './DashboardHeader'
import { DashboardNavItem } from '@/types/dashboard'
import { 
  LayoutDashboard, 
  FolderOpen, 
  MessageSquare, 
  FileText, 
  CreditCard, 
  User,
  Bell
} from 'lucide-react'

interface DashboardLayoutProps {
  children?: React.ReactNode
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const { user } = useAuth()
  const location = useLocation()

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true)
      }
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // GSAP animations
  useEffect(() => {
    const tl = gsap.timeline()
    
    tl.fromTo('.dashboard-sidebar', 
      { x: -300, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.6, ease: 'power2.out' }
    )
    .fromTo('.dashboard-main',
      { x: 50, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.6, ease: 'power2.out' },
      '-=0.3'
    )

    return () => {
      tl.kill()
    }
  }, [])

  // Navigation items
  const navItems: DashboardNavItem[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'LayoutDashboard',
      path: '/dashboard',
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: 'FolderOpen',
      path: '/dashboard/projects',
    },
    {
      id: 'messages',
      label: 'Messages',
      icon: 'MessageSquare',
      path: '/dashboard/messages',
      badge: 3 // This would come from actual unread count
    },
    {
      id: 'documents',
      label: 'Documents',
      icon: 'FileText',
      path: '/dashboard/documents',
    },
    {
      id: 'payments',
      label: 'Payments',
      icon: 'CreditCard',
      path: '/dashboard/payments',
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: 'User',
      path: '/dashboard/profile',
    }
  ]

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <div className="min-h-screen bg-agency-dark">
      {/* Sidebar */}
      <div className={cn(
        'dashboard-sidebar fixed inset-y-0 left-0 z-50 transition-all duration-300',
        sidebarCollapsed ? 'w-16' : 'w-64',
        isMobile && sidebarCollapsed && 'w-0'
      )}>
        <DashboardSidebar
          collapsed={sidebarCollapsed}
          onToggle={handleSidebarToggle}
          navItems={navItems}
          isMobile={isMobile}
        />
      </div>

      {/* Mobile overlay */}
      {isMobile && !sidebarCollapsed && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setSidebarCollapsed(true)}
        />
      )}

      {/* Main content */}
      <div className={cn(
        'dashboard-main transition-all duration-300',
        sidebarCollapsed ? 'ml-16' : 'ml-64',
        isMobile && 'ml-0'
      )}>
        {/* Header */}
        <DashboardHeader 
          user={user}
          onMenuClick={handleSidebarToggle}
          showMenuButton={isMobile}
        />

        {/* Page content */}
        <main className="p-4 md:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            {children || <Outlet />}
          </div>
        </main>
      </div>
    </div>
  )
}

export default DashboardLayout
