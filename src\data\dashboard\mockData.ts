import { 
  User, 
  Project, 
  Payment, 
  Message, 
  Document, 
  ProjectMilestone 
} from '@/lib/supabase'

// Mock user data
export const mockUser: User = {
  id: 'user-123',
  email: '<EMAIL>',
  full_name: '<PERSON>',
  phone: '+234 ************',
  created_at: '2024-01-15T10:00:00Z',
  updated_at: '2024-07-01T15:30:00Z',
  payment_status: 'paid',
  subscription_type: 'SME Kickstart Deal'
}

// Mock project milestones
export const mockMilestones: ProjectMilestone[] = [
  {
    id: 'milestone-1',
    project_id: 'project-1',
    title: 'Project Planning & Discovery',
    description: 'Initial consultation and project requirements gathering',
    status: 'completed',
    due_date: '2024-06-15T00:00:00Z',
    completed_date: '2024-06-14T16:30:00Z',
    order_index: 1,
    created_at: '2024-06-01T10:00:00Z',
    updated_at: '2024-06-14T16:30:00Z'
  },
  {
    id: 'milestone-2',
    project_id: 'project-1',
    title: 'Design & Wireframing',
    description: 'Create website mockups and design system',
    status: 'completed',
    due_date: '2024-06-30T00:00:00Z',
    completed_date: '2024-06-28T14:20:00Z',
    order_index: 2,
    created_at: '2024-06-01T10:00:00Z',
    updated_at: '2024-06-28T14:20:00Z'
  },
  {
    id: 'milestone-3',
    project_id: 'project-1',
    title: 'Development & Implementation',
    description: 'Build the website with all requested features',
    status: 'in_progress',
    due_date: '2024-07-15T00:00:00Z',
    order_index: 3,
    created_at: '2024-06-01T10:00:00Z',
    updated_at: '2024-07-01T09:15:00Z'
  },
  {
    id: 'milestone-4',
    project_id: 'project-1',
    title: 'Testing & Quality Assurance',
    description: 'Comprehensive testing and bug fixes',
    status: 'pending',
    due_date: '2024-07-25T00:00:00Z',
    order_index: 4,
    created_at: '2024-06-01T10:00:00Z',
    updated_at: '2024-06-01T10:00:00Z'
  },
  {
    id: 'milestone-5',
    project_id: 'project-1',
    title: 'Launch & Deployment',
    description: 'Deploy website and go live',
    status: 'pending',
    due_date: '2024-08-01T00:00:00Z',
    order_index: 5,
    created_at: '2024-06-01T10:00:00Z',
    updated_at: '2024-06-01T10:00:00Z'
  }
]

// Mock projects
export const mockProjects: Project[] = [
  {
    id: 'project-1',
    user_id: 'user-123',
    name: 'Business Website Redesign',
    description: 'Complete redesign of company website with modern UI/UX, mobile responsiveness, and SEO optimization.',
    package_type: 'SME Kickstart Deal',
    status: 'in_progress',
    progress_percentage: 65,
    start_date: '2024-06-01T00:00:00Z',
    estimated_completion: '2024-08-01T00:00:00Z',
    created_at: '2024-05-28T10:00:00Z',
    updated_at: '2024-07-01T15:30:00Z'
  },
  {
    id: 'project-2',
    user_id: 'user-123',
    name: 'E-commerce Platform',
    description: 'Development of online store with payment integration and inventory management.',
    package_type: 'Market Seller Pro Kit',
    status: 'planning',
    progress_percentage: 15,
    start_date: '2024-07-15T00:00:00Z',
    estimated_completion: '2024-09-30T00:00:00Z',
    created_at: '2024-07-01T14:20:00Z',
    updated_at: '2024-07-02T09:45:00Z'
  }
]

// Mock payments
export const mockPayments: Payment[] = [
  {
    id: 'payment-1',
    user_id: 'user-123',
    project_id: 'project-1',
    amount: 166750,
    currency: '₦',
    payment_method: 'Card',
    paystack_reference: 'T123456789',
    status: 'success',
    payment_date: '2024-05-28T14:30:00Z',
    created_at: '2024-05-28T14:25:00Z'
  },
  {
    id: 'payment-2',
    user_id: 'user-123',
    project_id: 'project-2',
    amount: 350750,
    currency: '₦',
    payment_method: 'Bank Transfer',
    paystack_reference: 'T987654321',
    status: 'pending',
    payment_date: '2024-07-01T10:15:00Z',
    created_at: '2024-07-01T10:10:00Z'
  }
]

// Mock messages
export const mockMessages: Message[] = [
  {
    id: 'message-1',
    user_id: 'user-123',
    project_id: 'project-1',
    sender_type: 'agency',
    sender_name: 'Sarah Johnson',
    subject: 'Website Design Review',
    content: 'Hi John! We\'ve completed the initial design mockups for your website. Please review the attached designs and let us know your feedback. We\'re excited to hear your thoughts!',
    is_read: false,
    created_at: '2024-07-02T09:30:00Z',
    updated_at: '2024-07-02T09:30:00Z'
  },
  {
    id: 'message-2',
    user_id: 'user-123',
    project_id: 'project-1',
    sender_type: 'client',
    sender_name: 'John Doe',
    subject: 'Website Design Review',
    content: 'The designs look fantastic! I love the color scheme and layout. Just a few minor adjustments needed on the contact page.',
    is_read: true,
    created_at: '2024-07-01T16:45:00Z',
    updated_at: '2024-07-01T16:45:00Z'
  },
  {
    id: 'message-3',
    user_id: 'user-123',
    sender_type: 'agency',
    sender_name: 'Mike Chen',
    subject: 'Project Timeline Update',
    content: 'Good news! We\'re ahead of schedule on your website development. The current milestone is 65% complete and we expect to finish early.',
    is_read: false,
    created_at: '2024-06-30T11:20:00Z',
    updated_at: '2024-06-30T11:20:00Z'
  },
  {
    id: 'message-4',
    user_id: 'user-123',
    project_id: 'project-2',
    sender_type: 'agency',
    sender_name: 'Lisa Wang',
    subject: 'E-commerce Project Kickoff',
    content: 'Welcome to your e-commerce project! We\'ll be starting the discovery phase next week. Please prepare a list of your product categories and any specific features you need.',
    is_read: true,
    created_at: '2024-07-01T14:30:00Z',
    updated_at: '2024-07-01T14:30:00Z'
  }
]

// Mock documents
export const mockDocuments: Document[] = [
  {
    id: 'doc-1',
    user_id: 'user-123',
    project_id: 'project-1',
    name: 'Website Development Contract.pdf',
    type: 'contract',
    file_url: '/documents/contract-123.pdf',
    file_size: 245760,
    mime_type: 'application/pdf',
    uploaded_by: 'Kavara Digital Team',
    created_at: '2024-05-28T15:00:00Z'
  },
  {
    id: 'doc-2',
    user_id: 'user-123',
    project_id: 'project-1',
    name: 'Payment Invoice - May 2024.pdf',
    type: 'invoice',
    file_url: '/documents/invoice-456.pdf',
    file_size: 128540,
    mime_type: 'application/pdf',
    uploaded_by: 'Billing System',
    created_at: '2024-05-28T14:35:00Z'
  },
  {
    id: 'doc-3',
    user_id: 'user-123',
    project_id: 'project-1',
    name: 'Website Mockups v2.zip',
    type: 'deliverable',
    file_url: '/documents/mockups-789.zip',
    file_size: 15728640,
    mime_type: 'application/zip',
    uploaded_by: 'Sarah Johnson',
    created_at: '2024-06-28T16:20:00Z'
  },
  {
    id: 'doc-4',
    user_id: 'user-123',
    project_id: 'project-1',
    name: 'Brand Guidelines.pdf',
    type: 'other',
    file_url: '/documents/brand-guidelines.pdf',
    file_size: 2048576,
    mime_type: 'application/pdf',
    uploaded_by: 'Design Team',
    created_at: '2024-06-15T10:45:00Z'
  },
  {
    id: 'doc-5',
    user_id: 'user-123',
    project_id: 'project-2',
    name: 'E-commerce Project Proposal.pdf',
    type: 'contract',
    file_url: '/documents/ecommerce-proposal.pdf',
    file_size: 512000,
    mime_type: 'application/pdf',
    uploaded_by: 'Lisa Wang',
    created_at: '2024-07-01T15:10:00Z'
  }
]

// Helper function to get mock data
export const getMockDashboardData = () => {
  return {
    user: mockUser,
    projects: mockProjects.map(project => ({
      ...project,
      project_milestones: mockMilestones.filter(m => m.project_id === project.id)
    })),
    payments: mockPayments,
    messages: mockMessages,
    documents: mockDocuments
  }
}
