import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { DashboardNavItem } from '@/types/dashboard'
import { 
  LayoutDashboard, 
  FolderOpen, 
  MessageSquare, 
  FileText, 
  CreditCard, 
  User,
  ChevronLeft,
  ChevronRight,
  Home
} from 'lucide-react'

interface DashboardSidebarProps {
  collapsed: boolean
  onToggle: () => void
  navItems: DashboardNavItem[]
  isMobile?: boolean
}

// Icon mapping
const iconMap = {
  LayoutDashboard,
  FolderOpen,
  MessageSquare,
  FileText,
  CreditCard,
  User,
  Home
}

const DashboardSidebar: React.FC<DashboardSidebarProps> = ({
  collapsed,
  onToggle,
  navItems,
  isMobile = false
}) => {
  const location = useLocation()

  const isActive = (path: string) => {
    if (path === '/dashboard') {
      return location.pathname === '/dashboard'
    }
    return location.pathname.startsWith(path)
  }

  const NavItem: React.FC<{ item: DashboardNavItem }> = ({ item }) => {
    const IconComponent = iconMap[item.icon as keyof typeof iconMap] || LayoutDashboard
    const active = isActive(item.path)

    const navButton = (
      <Button
        variant="ghost"
        className={cn(
          'w-full justify-start h-12 px-3 transition-all duration-200',
          active 
            ? 'bg-agency-green/10 text-agency-green border-r-2 border-agency-green' 
            : 'text-agency-white-muted hover:text-white hover:bg-agency-green/5',
          collapsed && 'justify-center px-2'
        )}
        asChild
      >
        <Link to={item.path}>
          <IconComponent className={cn(
            'h-5 w-5 flex-shrink-0',
            !collapsed && 'mr-3'
          )} />
          {!collapsed && (
            <>
              <span className="flex-1 text-left">{item.label}</span>
              {item.badge && item.badge > 0 && (
                <Badge 
                  variant="secondary" 
                  className="ml-auto bg-agency-green text-agency-dark text-xs"
                >
                  {item.badge}
                </Badge>
              )}
            </>
          )}
        </Link>
      </Button>
    )

    if (collapsed) {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            {navButton}
          </TooltipTrigger>
          <TooltipContent side="right" className="bg-agency-darker border-agency-green/20">
            <div className="flex items-center gap-2">
              <span>{item.label}</span>
              {item.badge && item.badge > 0 && (
                <Badge variant="secondary" className="bg-agency-green text-agency-dark text-xs">
                  {item.badge}
                </Badge>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      )
    }

    return navButton
  }

  if (isMobile && collapsed) {
    return null
  }

  return (
    <div className="h-full bg-agency-darker/50 backdrop-blur-sm border-r border-agency-green/10 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-agency-green/10">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <Link to="/" className="flex items-center">
              <img
                src="/logo-01.svg"
                alt="Kavara Digital"
                className="w-32 h-8"
              />
            </Link>
          )}
          
          {!isMobile && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggle}
              className="text-agency-white-muted hover:text-white hover:bg-agency-green/10 ml-auto"
            >
              {collapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navItems.map((item) => (
          <NavItem key={item.id} item={item} />
        ))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-agency-green/10">
        <Button
          variant="ghost"
          className={cn(
            'w-full justify-start h-12 px-3 text-agency-white-muted hover:text-white hover:bg-agency-green/5',
            collapsed && 'justify-center px-2'
          )}
          asChild
        >
          <Link to="/">
            <Home className={cn(
              'h-5 w-5 flex-shrink-0',
              !collapsed && 'mr-3'
            )} />
            {!collapsed && <span>Back to Site</span>}
          </Link>
        </Button>
      </div>
    </div>
  )
}

export default DashboardSidebar
