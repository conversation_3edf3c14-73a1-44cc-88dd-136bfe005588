
import { useEffect, useRef, useState } from 'react'
import { Link } from 'react-router-dom'
import { gsap } from 'gsap'
import {
  CheckCircle,
  Trophy,
  Download,
  Share2,
  X,
  Home,
  MessageCircle,
  Copy
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { toast } from '@/components/ui/sonner'
import AnimatedFloatingBlocks from '@/components/AnimatedFloatingBlocks'
import SEOHead from '@/components/SEOHead'

const ThankYou = () => {
  const heroRef = useRef<HTMLDivElement>(null)
  const successIconRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const [showShareMenu, setShowShareMenu] = useState(false)

  // Get order details from URL params or localStorage
  const urlParams = new URLSearchParams(window.location.search)
  const orderNumber =
    urlParams.get('order') || 'KD-' + Date.now().toString().slice(-6)
  const customerName = urlParams.get('name') || 'Valued Customer'
  const amount = urlParams.get('amount') || 'N/A'

  useEffect(() => {
    // Scroll to top on page load
    window.scrollTo(0, 0)

    // GSAP Animation Timeline
    const tl = gsap.timeline()

    // Success icon celebration animation
    tl.fromTo(
      successIconRef.current,
      { scale: 0, rotation: -180, opacity: 0 },
      {
        scale: 1,
        rotation: 0,
        opacity: 1,
        duration: 0.8,
        ease: 'back.out(1.7)'
      }
    ).to(
      successIconRef.current,
      {
        scale: 1.1,
        duration: 0.3,
        yoyo: true,
        repeat: 1,
        ease: 'power2.inOut'
      },
      '-=0.2'
    )

    // Staggered content animation
    tl.fromTo(
      heroRef.current?.children,
      { y: 50, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        stagger: 0.15,
        ease: 'power3.out'
      },
      '-=0.4'
    )

    // Content cards animation
    tl.fromTo(
      contentRef.current?.children,
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.5,
        stagger: 0.1,
        ease: 'power2.out'
      },
      '-=0.3'
    )

    // Cleanup
    return () => {
      tl.kill()
    }
  }, [])

  const handleCloseTab = () => {
    if (window.confirm('Are you sure you want to close this tab?')) {
      // Try different methods to close the tab
      if (window.history.length > 1) {
        window.history.back()
      } else {
        window.close()
        // Fallback: redirect to homepage if close doesn't work
        setTimeout(() => {
          window.location.href = '/'
        }, 1000)
      }
    }
  }

  const handleShare = (platform: string) => {
    const shareText = `Just made a purchase from KavaraDigital! 🎉 Excited to work with this amazing team. #KavaraDigital #WebDevelopment`
    const shareUrl = 'https://www.kavaradigital.online'

    let url = ''
    switch (platform) {
      case 'twitter':
        url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
          shareText
        )}&url=${encodeURIComponent(shareUrl)}`
        break
      case 'facebook':
        url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
          shareUrl
        )}`
        break
      case 'linkedin':
        url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
          shareUrl
        )}`
        break
      case 'copy':
        navigator.clipboard.writeText(`${shareText} ${shareUrl}`)
        toast.success('Link copied to clipboard!')
        setShowShareMenu(false)
        return
    }

    if (url) {
      window.open(url, '_blank', 'width=600,height=400')
      setShowShareMenu(false)
    }
  }

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden relative">
      <SEOHead />

      {/* Animated Background */}
      <AnimatedFloatingBlocks />

      {/* Close Tab Button */}
      <button
        onClick={handleCloseTab}
        className="fixed top-6 right-6 z-50 w-12 h-12 bg-agency-darker/80 hover:bg-agency-darker border border-agency-green/20 hover:border-agency-green/40 text-agency-white-muted hover:text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group backdrop-blur-sm"
        aria-label="Close tab"
      >
        <X size={20} className="group-hover:scale-110 transition-transform" />
      </button>

      {/* Main Content */}
      <main className="relative z-10 min-h-screen flex flex-col items-center justify-center px-4 py-16">
        {/* Hero Section */}
        <div ref={heroRef} className="text-center mb-12 max-w-4xl mx-auto">
          {/* Success Icon */}
          <div ref={successIconRef} className="mb-8">
            <div className="w-24 h-24 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-agency-green/30">
              <CheckCircle size={48} className="text-agency-green" />
            </div>
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Trophy size={20} className="text-agency-green" />
              <span className="text-agency-green font-medium">
                Purchase Successful
              </span>
              <Trophy size={20} className="text-agency-green" />
            </div>
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
            Thank You, <span className="text-gradient">{customerName}!</span>
          </h1>

          {/* Subheading */}
          <p className="text-xl md:text-2xl text-agency-white-muted mb-8 leading-relaxed">
            🎉 Your order has been received and we're absolutely thrilled to
            work with you! Get ready for an amazing digital transformation
            journey.
          </p>

          {/* Order Details */}
          <div className="bg-agency-darker/50 border border-agency-green/20 rounded-xl p-6 mb-8 backdrop-blur-sm">
            <h3 className="text-lg font-semibold text-white mb-4">
              Order Confirmation
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-left">
              <div>
                <span className="text-agency-white-muted">Order Number:</span>
                <p className="text-white font-mono font-medium">
                  {orderNumber}
                </p>
              </div>
              <div>
                <span className="text-agency-white-muted">Amount:</span>
                <p className="text-white font-medium">{amount}</p>
              </div>
              <div>
                <span className="text-agency-white-muted">Status:</span>
                <p className="text-agency-green font-medium flex items-center">
                  <CheckCircle size={16} className="mr-2" />
                  Confirmed
                </p>
              </div>
              <div>
                <span className="text-agency-white-muted">Date:</span>
                <p className="text-white font-medium">
                  {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Cards */}
        <div
          ref={contentRef}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto mb-12"
        >
          {/* Return Home */}
          <Card className="bg-agency-darker/50 border-agency-green/20 hover:border-agency-green/40 transition-all duration-300 card-hover backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Home size={24} className="text-agency-green" />
              </div>
              <h3 className="text-white font-semibold mb-2">Return Home</h3>
              <p className="text-agency-white-muted text-sm mb-4">
                Explore more of our services and solutions
              </p>
              <Link to="/">
                <Button className="w-full bg-agency-green hover:bg-agency-green/90 text-white">
                  Go Home
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Contact Support */}
          <Card className="bg-agency-darker/50 border-agency-green/20 hover:border-agency-green/40 transition-all duration-300 card-hover backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle size={24} className="text-agency-green" />
              </div>
              <h3 className="text-white font-semibold mb-2">Need Help?</h3>
              <p className="text-agency-white-muted text-sm mb-4">
                Our support team is here to assist you
              </p>
              <Link to="/contact">
                <Button
                  variant="outline"
                  className="w-full border-agency-green/30 text-agency-green hover:bg-agency-green/10"
                >
                  Contact Us
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Download Receipt */}
          <Card className="bg-agency-darker/50 border-agency-green/20 hover:border-agency-green/40 transition-all duration-300 card-hover backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Download size={24} className="text-agency-green" />
              </div>
              <h3 className="text-white font-semibold mb-2">Get Receipt</h3>
              <p className="text-agency-white-muted text-sm mb-4">
                Download your purchase confirmation
              </p>
              <Button
                variant="outline"
                className="w-full border-agency-green/30 text-agency-green hover:bg-agency-green/10"
                onClick={() =>
                  toast.success('Receipt will be sent to your email!')
                }
              >
                Download
              </Button>
            </CardContent>
          </Card>

          {/* Share */}
          <Card className="bg-agency-darker/50 border-agency-green/20 hover:border-agency-green/40 transition-all duration-300 card-hover backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Share2 size={24} className="text-agency-green" />
              </div>
              <h3 className="text-white font-semibold mb-2">Share Joy</h3>
              <p className="text-agency-white-muted text-sm mb-4">
                Tell others about your experience
              </p>
              <div className="relative">
                <Button
                  variant="outline"
                  className="w-full border-agency-green/30 text-agency-green hover:bg-agency-green/10"
                  onClick={() => setShowShareMenu(!showShareMenu)}
                >
                  Share
                </Button>

                {/* Share Menu */}
                {showShareMenu && (
                  <div className="absolute top-full left-0 right-0 mt-2 bg-agency-darker border border-agency-green/20 rounded-lg p-2 z-20 backdrop-blur-sm">
                    <div className="space-y-1">
                      <button
                        onClick={() => handleShare('twitter')}
                        className="w-full flex items-center justify-start px-3 py-2 rounded hover:bg-agency-green/10 transition-colors text-sm"
                      >
                        <Share2 size={14} className="text-blue-400 mr-2" />
                        <span className="text-white">Twitter</span>
                      </button>
                      <button
                        onClick={() => handleShare('facebook')}
                        className="w-full flex items-center justify-start px-3 py-2 rounded hover:bg-agency-green/10 transition-colors text-sm"
                      >
                        <Share2 size={14} className="text-blue-600 mr-2" />
                        <span className="text-white">Facebook</span>
                      </button>
                      <button
                        onClick={() => handleShare('linkedin')}
                        className="w-full flex items-center justify-start px-3 py-2 rounded hover:bg-agency-green/10 transition-colors text-sm"
                      >
                        <Share2 size={14} className="text-blue-500 mr-2" />
                        <span className="text-white">LinkedIn</span>
                      </button>
                      <button
                        onClick={() => handleShare('copy')}
                        className="w-full flex items-center justify-start px-3 py-2 rounded hover:bg-agency-green/10 transition-colors text-sm"
                      >
                        <Copy size={14} className="text-agency-green mr-2" />
                        <span className="text-white">Copy Link</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* What's Next Section */}
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            What Happens Next?
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-agency-green/30">
                <span className="text-2xl font-bold text-agency-green">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Confirmation Email
              </h3>
              <p className="text-agency-white-muted">
                You'll receive a detailed confirmation email within the next few
                minutes
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-agency-green/30">
                <span className="text-2xl font-bold text-agency-green">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Project Kickoff
              </h3>
              <p className="text-agency-white-muted">
                Our team will contact you within 24 hours to discuss your
                project details
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-agency-green/30">
                <span className="text-2xl font-bold text-agency-green">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Development Begins
              </h3>
              <p className="text-agency-white-muted">
                We'll start crafting your digital solution with regular updates
              </p>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center">
          <p className="text-lg text-agency-white-muted mb-6">
            Have questions? We're here to help every step of the way.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              onClick={handleCloseTab}
              variant="outline"
              className="border-agency-green/30 text-agency-green hover:bg-agency-green/10"
            >
              <X size={16} className="mr-2" />
              Close Tab
            </Button>
            <Link to="/">
              <Button className="bg-agency-green hover:bg-agency-green/90 text-white">
                <Home size={16} className="mr-2" />
                Return to Homepage
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}

export default ThankYou
