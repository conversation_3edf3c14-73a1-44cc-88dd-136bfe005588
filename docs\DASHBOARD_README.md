# Client Dashboard Documentation

## Overview

The Client Dashboard is a comprehensive portal for clients who have completed payment through the existing payment system. It provides project tracking, communication tools, document management, and payment history in a secure, user-friendly interface.

## Features

### 🔐 Authentication & Security

- **Supabase Authentication**: Secure user authentication with JWT tokens
- **Payment Verification**: Integration with Paystack to verify payment status
- **Protected Routes**: Dashboard access restricted to paid clients only
- **Row Level Security**: Users can only access their own data

### 📊 Dashboard Overview

- **Project Statistics**: Total, active, and completed projects
- **Quick Metrics**: Unread messages, documents, and spending summary
- **Recent Activity**: Latest project updates and communications
- **Quick Actions**: Common tasks and shortcuts

### 📁 Project Management

- **Project Tracking**: Real-time progress monitoring with percentage completion
- **Milestone System**: Visual milestone tracking with status indicators
- **Timeline View**: Start dates, due dates, and completion estimates
- **Status Management**: Planning, In Progress, Review, Completed, On Hold

### 💬 Communication Hub

- **Message Threads**: Organized conversations by subject/project
- **Real-time Messaging**: Send and receive messages with the team
- **Unread Indicators**: Clear visual indicators for new messages
- **Project Context**: Messages linked to specific projects

### 📄 Document Management

- **Categorized Documents**: Contracts, Invoices, Deliverables, Other
- **File Preview**: In-browser document preview capabilities
- **Download System**: Secure file downloads with access logging
- **Upload Tracking**: See who uploaded files and when

### 💳 Payment History

- **Transaction History**: Complete payment record with Paystack integration
- **Invoice Management**: Access to all invoices and receipts
- **Payment Status**: Real-time payment verification
- **Billing Summary**: Total paid, pending amounts, and subscription status

### 👤 Profile Management

- **User Information**: Editable profile with contact details
- **Account Settings**: Password changes and preferences
- **Payment Status**: Current subscription and verification status
- **Account Actions**: Security and preference management

## Technical Architecture

### Frontend Stack

- **React 18** with TypeScript for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for responsive styling
- **shadcn/ui** for consistent UI components
- **GSAP** for smooth animations
- **React Router** for client-side routing
- **React Query** for data fetching and caching

### Backend Integration

- **Supabase** for authentication and database
- **Paystack API** for payment verification
- **Real-time subscriptions** for live updates
- **Row Level Security** for data protection

### Database Schema

```sql
-- Users table
users (
  id: uuid (primary key)
  email: text (unique)
  full_name: text
  phone: text
  payment_status: enum ('pending', 'paid', 'expired')
  subscription_type: text
  created_at: timestamp
  updated_at: timestamp
)

-- Projects table
projects (
  id: uuid (primary key)
  user_id: uuid (foreign key)
  name: text
  description: text
  package_type: text
  status: enum ('planning', 'in_progress', 'review', 'completed', 'on_hold')
  progress_percentage: integer
  start_date: timestamp
  estimated_completion: timestamp
  actual_completion: timestamp
  created_at: timestamp
  updated_at: timestamp
)

-- Project Milestones table
project_milestones (
  id: uuid (primary key)
  project_id: uuid (foreign key)
  title: text
  description: text
  status: enum ('pending', 'in_progress', 'completed')
  due_date: timestamp
  completed_date: timestamp
  order_index: integer
  created_at: timestamp
  updated_at: timestamp
)

-- Messages table
messages (
  id: uuid (primary key)
  user_id: uuid (foreign key)
  project_id: uuid (foreign key, nullable)
  sender_type: enum ('client', 'agency')
  sender_name: text
  subject: text
  content: text
  is_read: boolean
  created_at: timestamp
  updated_at: timestamp
)

-- Documents table
documents (
  id: uuid (primary key)
  user_id: uuid (foreign key)
  project_id: uuid (foreign key, nullable)
  name: text
  type: enum ('contract', 'invoice', 'deliverable', 'other')
  file_url: text
  file_size: integer
  mime_type: text
  uploaded_by: text
  created_at: timestamp
)

-- Payments table
payments (
  id: uuid (primary key)
  user_id: uuid (foreign key)
  project_id: uuid (foreign key, nullable)
  amount: decimal
  currency: text
  payment_method: text
  paystack_reference: text
  status: enum ('pending', 'success', 'failed')
  payment_date: timestamp
  created_at: timestamp
)
```

## File Structure

```
src/
├── components/
│   ├── dashboard/
│   │   ├── DashboardLayout.tsx      # Main layout wrapper
│   │   ├── DashboardSidebar.tsx     # Navigation sidebar
│   │   ├── DashboardHeader.tsx      # Top header with user menu
│   │   ├── DashboardOverview.tsx    # Main dashboard page
│   │   ├── ProjectsPage.tsx         # Projects management
│   │   ├── MessagesPage.tsx         # Communication hub
│   │   ├── DocumentsPage.tsx        # Document management
│   │   ├── PaymentsPage.tsx         # Payment history
│   │   └── ProfilePage.tsx          # User profile
│   └── ProtectedRoute.tsx           # Route protection wrapper
├── contexts/
│   └── AuthContext.tsx              # Authentication context
├── hooks/
│   └── useDashboard.ts              # Dashboard data hooks
├── lib/
│   └── supabase.ts                  # Supabase configuration
├── types/
│   └── dashboard.ts                 # TypeScript interfaces
├── data/
│   └── dashboard/
│       └── mockData.ts              # Development mock data
└── pages/
    ├── Dashboard.tsx                # Main dashboard router
    └── Login.tsx                    # Authentication page
```

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file with:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 2. Supabase Setup

1. Create a new Supabase project
2. Run the database schema (see schema above)
3. Configure Row Level Security policies
4. Set up authentication providers

### 3. Paystack Integration

1. Configure Paystack webhook endpoints
2. Set up payment verification endpoints
3. Update payment status in database

### 4. Development

```bash
# Install dependencies
bun install

# Start development server
bun dev

# Access dashboard at http://localhost:8080/dashboard
```

---

For each table, I've created policies that:

Users Table:

Users can only view and update their own user record
Users cannot delete their accounts directly (this would typically be handled through a special API)
User creation is handled by the auth system, not direct inserts
Projects Table:

Users can only view, create, update, and delete their own projects
Project Milestones Table:

Users can only view, create, update, and delete milestones for projects they own
Uses a subquery to verify project ownership
Messages Table:

Users can only view, create, update, and delete their own messages
Documents Table:

Users can only view, create, update, and delete their own documents
Payments Table:

Users can only view, create, update, and delete their own payment records
Additional Helpful Database Objects
User Projects View:

Created a secure view (user_projects_with_milestones) with security_invoker=on that joins projects with their milestones
This makes it easier to query project data with related milestones in a single request
The view automatically filters to show only the current user's projects
Project Statistics Function:

Created a function (get_user_project_stats) that calculates various statistics for a user's projects
Returns counts of projects by status, average completion time, and milestone statistics
Can be called without parameters to get the current user's stats, or with a specific user_id for admin functions

---

## Usage

### For Clients

1. **Access**: Navigate to `/dashboard` (redirects to login if not authenticated)
2. **Login**: Use email/password or demo credentials
3. **Navigation**: Use sidebar to access different sections
4. **Projects**: View progress, milestones, and timelines
5. **Messages**: Communicate with the team
6. **Documents**: Download contracts, invoices, and deliverables
7. **Payments**: View transaction history and billing

### For Developers

1. **Authentication**: Use `useAuth()` hook for user state
2. **Data Fetching**: Use dashboard hooks (`useDashboard`, `useProjects`, etc.)
3. **Protection**: Wrap routes with `ProtectedRoute` component
4. **Styling**: Follow existing Tailwind/shadcn patterns
5. **Animations**: Use GSAP for smooth transitions

## Security Considerations

### Authentication

- JWT tokens with automatic refresh
- Secure session management
- Protected route implementation

### Authorization

- Row Level Security in Supabase
- User-specific data access only
- Payment status verification

### Data Protection

- Encrypted data transmission
- Secure file storage
- Access logging for sensitive operations

## Customization

### Theming

- Uses existing agency color scheme
- Consistent with main site design
- Dark theme optimized for professional use

### Responsive Design

- Mobile-first approach
- Collapsible sidebar for mobile
- Touch-friendly interactions

### Animations

- GSAP-powered smooth transitions
- Staggered loading animations
- Hover and interaction feedback

## Future Enhancements

### Planned Features

- [ ] Real-time notifications
- [ ] File upload functionality
- [ ] Advanced project filtering
- [ ] Calendar integration
- [ ] Mobile app companion
- [ ] Advanced analytics
- [ ] Team collaboration tools
- [ ] API access for integrations

### Performance Optimizations

- [ ] Image optimization
- [ ] Code splitting
- [ ] Caching strategies
- [ ] Progressive loading
- [ ] Offline capabilities

## Support

For technical support or questions:

- **Email**: <EMAIL>
- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs through the project repository

## License

This dashboard is part of the Kavara Digital client portal system and is proprietary software.
