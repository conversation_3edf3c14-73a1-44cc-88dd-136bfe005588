# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/3657573a-2f91-4c97-9c4b-43dcc478934c

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/3657573a-2f91-4c97-9c4b-43dcc478934c) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## Key Features

### 🎉 Thank You Page
- **Engaging post-purchase experience** with GSAP animations
- **Dynamic order confirmation** with URL parameter support
- **Social sharing functionality** for customer engagement
- **Close tab feature** for improved UX
- **Responsive design** optimized for all devices

### 🌐 Digital Agency Website
- **Modern responsive design** with dark theme
- **Service showcase** with detailed offerings
- **Portfolio section** with project highlights
- **Contact forms** and WhatsApp integration
- **SEO optimized** with dynamic meta tags

### 📱 Mobile-First Design
- **Bottom navigation** for mobile users
- **Touch-friendly interfaces** throughout
- **Optimized animations** for mobile performance

## What technologies are used for this project?

This project is built with:

- **Vite** - Fast build tool and dev server
- **TypeScript** - Type-safe JavaScript
- **React** - UI library with hooks
- **shadcn-ui** - Modern component library
- **Tailwind CSS** - Utility-first CSS framework
- **GSAP** - Professional animation library
- **React Router** - Client-side routing
- **Lucide React** - Beautiful icon library

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/3657573a-2f91-4c97-9c4b-43dcc478934c) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
