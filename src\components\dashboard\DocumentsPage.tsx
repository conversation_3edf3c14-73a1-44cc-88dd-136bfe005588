import React, { useEffect } from 'react'
import { gsap } from 'gsap'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useDocuments } from '@/hooks/useDashboard'
import {
  FileText,
  Download,
  Eye,
  Calendar,
  Loader2,
  File,
  FileImage,
  FileSpreadsheet,
  FileAudio
} from 'lucide-react'

const DocumentsPage: React.FC = () => {
  const { data: documentCategories, isLoading, error } = useDocuments()

  useEffect(() => {
    if (!isLoading && documentCategories) {
      gsap.fromTo(
        '.document-category',
        { y: 30, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power2.out'
        }
      )
    }
  }, [isLoading, documentCategories])

  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes('pdf'))
      return <FileAudio className="h-5 w-5 text-red-500" />
    if (mimeType.includes('image'))
      return <FileImage className="h-5 w-5 text-blue-500" />
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel'))
      return <FileSpreadsheet className="h-5 w-5 text-green-500" />
    return <File className="h-5 w-5 text-agency-white-muted" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = (documentId: string, fileName: string) => {
    // This would integrate with your file storage system
    console.log('Downloading document:', documentId, fileName)
    // For now, we'll just show a placeholder
    alert(`Download functionality will be implemented for: ${fileName}`)
  }

  const handlePreview = (documentId: string, fileName: string) => {
    // This would open a preview modal or new tab
    console.log('Previewing document:', documentId, fileName)
    alert(`Preview functionality will be implemented for: ${fileName}`)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-agency-green mx-auto mb-4" />
          <p className="text-agency-white-muted">Loading your documents...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">Failed to load documents</p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  const hasDocuments = documentCategories?.some(
    (category) => category.count > 0
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Documents</h1>
          <p className="text-agency-white-muted mt-1">
            Access your contracts, invoices, deliverables, and other project
            files
          </p>
        </div>
        <Button className="bg-agency-green hover:bg-agency-green/90 text-agency-dark">
          <Download className="mr-2 h-4 w-4" />
          Download All
        </Button>
      </div>

      {/* Document Categories */}
      {hasDocuments ? (
        <div className="space-y-6">
          {documentCategories?.map(
            (category) =>
              category.count > 0 && (
                <Card
                  key={category.type}
                  className="document-category bg-agency-darker/50 border-agency-green/10"
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-agency-green/10 rounded-lg">
                          <FileText className="h-5 w-5 text-agency-green" />
                        </div>
                        <div>
                          <CardTitle className="text-white">
                            {category.label}
                          </CardTitle>
                          <CardDescription className="text-agency-white-muted">
                            {category.count} file
                            {category.count !== 1 ? 's' : ''}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge className="bg-agency-green/10 text-agency-green border-agency-green/20">
                        {category.count}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent>
                    <div className="space-y-3">
                      {category.documents.map((document) => (
                        <div
                          key={document.id}
                          className="flex items-center justify-between p-3 bg-agency-dark/50 rounded-lg hover:bg-agency-dark/70 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            {getFileIcon(document.mime_type)}
                            <div>
                              <h4 className="text-white font-medium">
                                {document.name}
                              </h4>
                              <div className="flex items-center gap-4 text-xs text-agency-white-muted">
                                <span>
                                  {formatFileSize(document.file_size)}
                                </span>
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {new Date(
                                    document.created_at
                                  ).toLocaleDateString()}
                                </div>
                                <span>by {document.uploaded_by}</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() =>
                                handlePreview(document.id, document.name)
                              }
                              className="text-agency-white-muted hover:text-white hover:bg-agency-green/10"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() =>
                                handleDownload(document.id, document.name)
                              }
                              className="text-agency-green hover:bg-agency-green/10"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
          )}
        </div>
      ) : (
        <Card className="bg-agency-darker/50 border-agency-green/10">
          <CardContent className="text-center py-12">
            <FileText className="h-12 w-12 text-agency-white-muted mx-auto mb-4" />
            <h3 className="text-white text-lg font-medium mb-2">
              No Documents Yet
            </h3>
            <p className="text-agency-white-muted mb-6">
              Your project documents, contracts, and deliverables will appear
              here once they're available.
            </p>
            <Button variant="outline">Contact Support</Button>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card className="bg-agency-darker/50 border-agency-green/10">
        <CardHeader>
          <CardTitle className="text-white">Document Help</CardTitle>
          <CardDescription className="text-agency-white-muted">
            Need help with your documents?
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start">
              <FileText className="mr-2 h-4 w-4" />
              Request Document
            </Button>
            <Button variant="outline" className="justify-start">
              <Download className="mr-2 h-4 w-4" />
              Download Guide
            </Button>
            <Button variant="outline" className="justify-start">
              <Eye className="mr-2 h-4 w-4" />
              View Samples
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default DocumentsPage
