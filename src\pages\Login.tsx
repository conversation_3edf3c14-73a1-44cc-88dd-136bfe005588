import React, { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { gsap } from 'gsap'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2, Mail, Lock, ArrowLeft } from 'lucide-react'

const Login: React.FC = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { signIn, user } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      const from = location.state?.from?.pathname || '/dashboard'
      navigate(from, { replace: true })
    }
  }, [user, navigate, location])

  // GSAP animations
  useEffect(() => {
    gsap.fromTo('.login-card',
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
    )
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email || !password) return

    setIsLoading(true)
    try {
      await signIn(email, password)
      const from = location.state?.from?.pathname || '/dashboard'
      navigate(from, { replace: true })
    } catch (error) {
      console.error('Login failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-agency-dark flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Back to site link */}
        <div className="mb-6">
          <Button variant="ghost" asChild className="text-agency-white-muted hover:text-white">
            <Link to="/">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Site
            </Link>
          </Button>
        </div>

        {/* Login Card */}
        <Card className="login-card bg-agency-darker/50 border-agency-green/20">
          <CardHeader className="text-center">
            <Link to="/" className="flex justify-center mb-4">
              <img
                src="/logo-01.svg"
                alt="Kavara Digital"
                className="w-40 h-10"
              />
            </Link>
            <CardTitle className="text-white text-2xl">Welcome Back</CardTitle>
            <CardDescription className="text-agency-white-muted">
              Sign in to access your client dashboard
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-agency-white-muted" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 bg-agency-dark border-agency-green/20 text-white"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-white">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-agency-white-muted" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10 bg-agency-dark border-agency-green/20 text-white"
                    required
                  />
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-agency-green hover:bg-agency-green/90 text-agency-dark font-medium"
                disabled={isLoading || !email || !password}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>

            <div className="mt-6 text-center space-y-4">
              <Button variant="link" className="text-agency-green hover:text-agency-green/80">
                Forgot your password?
              </Button>
              
              <div className="text-sm text-agency-white-muted">
                Don't have an account?{' '}
                <Link to="/contact" className="text-agency-green hover:underline">
                  Contact us to get started
                </Link>
              </div>
            </div>

            {/* Demo Credentials */}
            <div className="mt-6 p-4 bg-agency-dark/50 rounded-lg border border-agency-green/10">
              <h4 className="text-white font-medium mb-2">Demo Access</h4>
              <p className="text-xs text-agency-white-muted mb-2">
                For testing purposes, use these credentials:
              </p>
              <div className="text-xs text-agency-white-muted space-y-1">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> demo123</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-3"
                onClick={() => {
                  setEmail('<EMAIL>')
                  setPassword('demo123')
                }}
              >
                Use Demo Credentials
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Help Text */}
        <div className="mt-6 text-center">
          <p className="text-xs text-agency-white-muted">
            Need help? Contact us at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-agency-green hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}

export default Login
