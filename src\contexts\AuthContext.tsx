import React, { createContext, useContext, useEffect, useState } from 'react'
import { User as SupabaseUser, Session } from '@supabase/supabase-js'
import { supabase, dbHelpers, User } from '@/lib/supabase'
import { mockUser } from '@/data/dashboard/mockData'
import { toast } from '@/components/ui/sonner'

// Check if we should use mock data
const useMockAuth =
  !import.meta.env.VITE_SUPABASE_URL ||
  import.meta.env.VITE_ENABLE_MOCK_DATA === 'true'

interface AuthContextType {
  user: User | null
  supabaseUser: SupabaseUser | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (
    email: string,
    password: string,
    fullName: string,
    phone?: string
  ) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  isPaymentVerified: boolean
  refreshPaymentStatus: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [isPaymentVerified, setIsPaymentVerified] = useState(false)

  // Initialize auth state
  useEffect(() => {
    if (useMockAuth) {
      // For demo purposes, start with no user
      setLoading(false)
      return
    }

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setSupabaseUser(session?.user ?? null)
      if (session?.user) {
        loadUserProfile(session.user.id)
      } else {
        setLoading(false)
      }
    })

    // Listen for auth changes
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      setSupabaseUser(session?.user ?? null)

      if (session?.user) {
        await loadUserProfile(session.user.id)
      } else {
        setUser(null)
        setIsPaymentVerified(false)
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  // Load user profile from database
  const loadUserProfile = async (userId: string) => {
    try {
      const userProfile = await dbHelpers.getUserById(userId)
      setUser(userProfile)

      // Check payment status
      const paymentVerified = await dbHelpers.verifyPaymentStatus(userId)
      setIsPaymentVerified(paymentVerified)
    } catch (error) {
      console.error('Error loading user profile:', error)
      // If user doesn't exist in our database, create a basic profile
      if (supabaseUser) {
        const newUser: Partial<User> = {
          id: supabaseUser.id,
          email: supabaseUser.email!,
          full_name: supabaseUser.user_metadata?.full_name || '',
          phone: supabaseUser.user_metadata?.phone || '',
          payment_status: 'pending'
        }

        try {
          const { data, error } = await supabase
            .from('users')
            .insert([newUser])
            .select()
            .single()

          if (!error && data) {
            setUser(data as User)
          }
        } catch (insertError) {
          console.error('Error creating user profile:', insertError)
        }
      }
    } finally {
      setLoading(false)
    }
  }

  // Sign in
  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      if (useMockAuth) {
        // Mock authentication for demo
        if (email === '<EMAIL>' && password === 'demo123') {
          setUser(mockUser)
          setIsPaymentVerified(true)
          toast.success('Successfully signed in!')
          return
        } else {
          throw new Error('Invalid credentials')
        }
      }

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) throw error
      toast.success('Successfully signed in!')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign in')
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Sign up
  const signUp = async (
    email: string,
    password: string,
    fullName: string,
    phone?: string
  ) => {
    setLoading(true)
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            phone: phone
          }
        }
      })

      if (error) throw error
      toast.success('Check your email for the confirmation link!')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign up')
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Sign out
  const signOut = async () => {
    setLoading(true)
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      toast.success('Successfully signed out!')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign out')
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })

      if (error) throw error
      toast.success('Password reset email sent!')
    } catch (error: any) {
      toast.error(error.message || 'Failed to send reset email')
      throw error
    }
  }

  // Refresh payment status
  const refreshPaymentStatus = async () => {
    if (user) {
      try {
        const paymentVerified = await dbHelpers.verifyPaymentStatus(user.id)
        setIsPaymentVerified(paymentVerified)
      } catch (error) {
        console.error('Error refreshing payment status:', error)
      }
    }
  }

  const value: AuthContextType = {
    user,
    supabaseUser,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    isPaymentVerified,
    refreshPaymentStatus
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
