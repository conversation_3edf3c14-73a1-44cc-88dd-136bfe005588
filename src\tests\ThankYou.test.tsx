import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { vi } from 'vitest'
import ThankYou from '../pages/ThankYou'

// Mock GSAP
vi.mock('gsap', () => ({
  gsap: {
    timeline: () => ({
      fromTo: vi.fn().mockReturnThis(),
      to: vi.fn().mockReturnThis(),
      kill: vi.fn()
    }),
    fromTo: vi.fn(),
    to: vi.fn()
  }
}))

// Mock components
vi.mock('../components/AnimatedFloatingBlocks', () => ({
  default: () => <div data-testid="animated-floating-blocks" />
}))

vi.mock('../components/SEOHead', () => ({
  default: () => <div data-testid="seo-head" />
}))

// Mock toast
vi.mock('../components/ui/sonner', () => ({
  toast: {
    success: vi.fn()
  }
}))

// Mock window methods
const mockWindowOpen = vi.fn()
const mockWindowClose = vi.fn()
const mockWindowConfirm = vi.fn()

Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true
})

Object.defineProperty(window, 'close', {
  value: mockWindowClose,
  writable: true
})

Object.defineProperty(window, 'confirm', {
  value: mockWindowConfirm,
  writable: true
})

// Mock clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn()
  }
})

const renderThankYou = (search = '') => {
  // Mock location search
  delete window.location
  window.location = { search } as any

  return render(
    <BrowserRouter>
      <ThankYou />
    </BrowserRouter>
  )
}

describe('ThankYou Page', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the thank you page with default values', () => {
    renderThankYou()

    expect(screen.getByText(/Thank You/)).toBeInTheDocument()
    expect(screen.getByText(/Valued Customer/)).toBeInTheDocument()
    expect(screen.getByText(/Your order has been received/)).toBeInTheDocument()
    expect(screen.getByText(/Purchase Successful/)).toBeInTheDocument()
  })

  it('displays custom order details from URL parameters', () => {
    renderThankYou('?order=KD-123456&name=John%20Doe&amount=₦350,750')

    expect(screen.getByText(/John Doe/)).toBeInTheDocument()
    expect(screen.getByText(/KD-123456/)).toBeInTheDocument()
    expect(screen.getByText(/₦350,750/)).toBeInTheDocument()
  })

  it('shows order confirmation details', () => {
    renderThankYou()

    expect(screen.getByText(/Order Confirmation/)).toBeInTheDocument()
    expect(screen.getByText(/Order Number:/)).toBeInTheDocument()
    expect(screen.getByText(/Amount:/)).toBeInTheDocument()
    expect(screen.getByText(/Status:/)).toBeInTheDocument()
    expect(screen.getByText(/Confirmed/)).toBeInTheDocument()
  })

  it('displays action cards', () => {
    renderThankYou()

    expect(screen.getByText(/Return Home/)).toBeInTheDocument()
    expect(screen.getByText(/Need Help\?/)).toBeInTheDocument()
    expect(screen.getByText(/Get Receipt/)).toBeInTheDocument()
    expect(screen.getByText(/Share Joy/)).toBeInTheDocument()
  })

  it('shows what happens next section', () => {
    renderThankYou()

    expect(screen.getByText(/What Happens Next\?/)).toBeInTheDocument()
    expect(screen.getByText(/Confirmation Email/)).toBeInTheDocument()
    expect(screen.getByText(/Project Kickoff/)).toBeInTheDocument()
    expect(screen.getByText(/Development Begins/)).toBeInTheDocument()
  })

  it('handles close tab functionality with confirmation', () => {
    mockWindowConfirm.mockReturnValue(true)
    renderThankYou()

    const closeButton = screen.getAllByText(/Close Tab/)[0]
    fireEvent.click(closeButton)

    expect(mockWindowConfirm).toHaveBeenCalledWith('Are you sure you want to close this tab?')
  })

  it('does not close tab when user cancels confirmation', () => {
    mockWindowConfirm.mockReturnValue(false)
    renderThankYou()

    const closeButton = screen.getAllByText(/Close Tab/)[0]
    fireEvent.click(closeButton)

    expect(mockWindowConfirm).toHaveBeenCalled()
    expect(mockWindowClose).not.toHaveBeenCalled()
  })

  it('opens share menu when share button is clicked', async () => {
    renderThankYou()

    const shareButton = screen.getByRole('button', { name: /Share/ })
    fireEvent.click(shareButton)

    await waitFor(() => {
      expect(screen.getByText(/Twitter/)).toBeInTheDocument()
      expect(screen.getByText(/Facebook/)).toBeInTheDocument()
      expect(screen.getByText(/LinkedIn/)).toBeInTheDocument()
      expect(screen.getByText(/Copy Link/)).toBeInTheDocument()
    })
  })

  it('handles social media sharing', async () => {
    renderThankYou()

    const shareButton = screen.getByRole('button', { name: /Share/ })
    fireEvent.click(shareButton)

    await waitFor(() => {
      const twitterButton = screen.getByText(/Twitter/)
      fireEvent.click(twitterButton)
    })

    expect(mockWindowOpen).toHaveBeenCalledWith(
      expect.stringContaining('twitter.com/intent/tweet'),
      '_blank',
      'width=600,height=400'
    )
  })

  it('handles copy link functionality', async () => {
    renderThankYou()

    const shareButton = screen.getByRole('button', { name: /Share/ })
    fireEvent.click(shareButton)

    await waitFor(() => {
      const copyButton = screen.getByText(/Copy Link/)
      fireEvent.click(copyButton)
    })

    expect(navigator.clipboard.writeText).toHaveBeenCalled()
  })

  it('renders navigation links', () => {
    renderThankYou()

    expect(screen.getByRole('link', { name: /Go Home/ })).toHaveAttribute('href', '/')
    expect(screen.getByRole('link', { name: /Contact Us/ })).toHaveAttribute('href', '/contact')
    expect(screen.getByRole('link', { name: /Return to Homepage/ })).toHaveAttribute('href', '/')
  })

  it('includes SEO head component', () => {
    renderThankYou()

    expect(screen.getByTestId('seo-head')).toBeInTheDocument()
  })

  it('includes animated floating blocks', () => {
    renderThankYou()

    expect(screen.getByTestId('animated-floating-blocks')).toBeInTheDocument()
  })
})
