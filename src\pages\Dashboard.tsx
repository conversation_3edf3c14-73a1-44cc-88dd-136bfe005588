import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import DashboardOverview from '@/components/dashboard/DashboardOverview'
import ProjectsPage from '@/components/dashboard/ProjectsPage'
import MessagesPage from '@/components/dashboard/MessagesPage'
import DocumentsPage from '@/components/dashboard/DocumentsPage'
import PaymentsPage from '@/components/dashboard/PaymentsPage'
import ProfilePage from '@/components/dashboard/ProfilePage'
import { useAuth } from '@/contexts/AuthContext'

gsap.registerPlugin(ScrollTrigger)

const Dashboard: React.FC = () => {
  const { user } = useAuth()

  useEffect(() => {
    // Set up GSAP defaults for dashboard
    gsap.config({
      nullTargetWarn: false
    })

    // Clean up on unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  // Update page title
  useEffect(() => {
    document.title = 'Dashboard - Kavara Digital'
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', 'Manage your projects, track progress, and communicate with the Kavara Digital team through your personalized client dashboard.')
    }

    return () => {
      document.title = 'Kavara Digital - Premium Web Development & Digital Marketing Agency'
    }
  }, [])

  return (
    <DashboardLayout>
      <Routes>
        {/* Dashboard Overview */}
        <Route index element={<DashboardOverview />} />
        
        {/* Projects Section */}
        <Route path="projects" element={<ProjectsPage />} />
        
        {/* Messages Section */}
        <Route path="messages" element={<MessagesPage />} />
        <Route path="messages/:threadId" element={<MessagesPage />} />
        
        {/* Documents Section */}
        <Route path="documents" element={<DocumentsPage />} />
        
        {/* Payments Section */}
        <Route path="payments" element={<PaymentsPage />} />
        
        {/* Profile Section */}
        <Route path="profile" element={<ProfilePage />} />
        
        {/* Redirect any unknown routes to dashboard overview */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </DashboardLayout>
  )
}

export default Dashboard
