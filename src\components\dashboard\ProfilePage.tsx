import React, { useState, useEffect } from 'react'
import { gsap } from 'gsap'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { 
  User, 
  Mail,
  Phone,
  Calendar,
  Shield,
  Edit,
  Save,
  X,
  Camera,
  Loader2
} from 'lucide-react'

const ProfilePage: React.FC = () => {
  const { user, isPaymentVerified } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    full_name: user?.full_name || '',
    email: user?.email || '',
    phone: user?.phone || ''
  })

  useEffect(() => {
    gsap.fromTo('.profile-card',
      { y: 30, opacity: 0 },
      { 
        y: 0, 
        opacity: 1, 
        duration: 0.6, 
        stagger: 0.1,
        ease: 'power2.out'
      }
    )
  }, [])

  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.full_name || '',
        email: user.email || '',
        phone: user.phone || ''
      })
    }
  }, [user])

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // This would integrate with your user update API
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      setIsEditing(false)
      // Show success message
    } catch (error) {
      console.error('Failed to update profile:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      full_name: user?.full_name || '',
      email: user?.email || '',
      phone: user?.phone || ''
    })
    setIsEditing(false)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500/10 text-green-500 border-green-500/20">Active</Badge>
      case 'pending':
        return <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">Pending</Badge>
      case 'expired':
        return <Badge className="bg-red-500/10 text-red-500 border-red-500/20">Expired</Badge>
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Unknown</Badge>
    }
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-agency-green mx-auto mb-4" />
          <p className="text-agency-white-muted">Loading profile...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Profile</h1>
          <p className="text-agency-white-muted mt-1">
            Manage your account information and preferences
          </p>
        </div>
        {!isEditing && (
          <Button 
            className="bg-agency-green hover:bg-agency-green/90 text-agency-dark"
            onClick={() => setIsEditing(true)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Profile
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <Card className="profile-card bg-agency-darker/50 border-agency-green/10 lg:col-span-1">
          <CardHeader className="text-center">
            <div className="relative mx-auto">
              <Avatar className="h-24 w-24 mx-auto">
                <AvatarImage src={user.email ? `https://api.dicebear.com/7.x/initials/svg?seed=${user.email}` : ''} />
                <AvatarFallback className="bg-agency-green text-agency-dark text-xl">
                  {getInitials(user.full_name || 'User')}
                </AvatarFallback>
              </Avatar>
              <Button
                size="icon"
                className="absolute -bottom-2 -right-2 h-8 w-8 bg-agency-green hover:bg-agency-green/90 text-agency-dark"
              >
                <Camera className="h-4 w-4" />
              </Button>
            </div>
            <CardTitle className="text-white mt-4">{user.full_name || 'User'}</CardTitle>
            <CardDescription className="text-agency-white-muted">
              {user.email}
            </CardDescription>
            <div className="flex justify-center mt-2">
              {getPaymentStatusBadge(user.payment_status)}
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3 text-sm">
              <Calendar className="h-4 w-4 text-agency-white-muted" />
              <span className="text-agency-white-muted">
                Member since {new Date(user.created_at).toLocaleDateString()}
              </span>
            </div>
            
            <div className="flex items-center gap-3 text-sm">
              <Shield className="h-4 w-4 text-agency-white-muted" />
              <span className="text-agency-white-muted">
                {isPaymentVerified ? 'Verified Client' : 'Pending Verification'}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Profile Information */}
        <Card className="profile-card bg-agency-darker/50 border-agency-green/10 lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white">Personal Information</CardTitle>
                <CardDescription className="text-agency-white-muted">
                  Update your personal details and contact information
                </CardDescription>
              </div>
              {isEditing && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={isSaving}
                    className="bg-agency-green hover:bg-agency-green/90 text-agency-dark"
                  >
                    {isSaving ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="mr-2 h-4 w-4" />
                    )}
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isSaving}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="full_name" className="text-white">Full Name</Label>
                {isEditing ? (
                  <Input
                    id="full_name"
                    value={formData.full_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                    className="bg-agency-dark border-agency-green/20 text-white"
                  />
                ) : (
                  <div className="flex items-center gap-2 p-3 bg-agency-dark/50 rounded-md">
                    <User className="h-4 w-4 text-agency-white-muted" />
                    <span className="text-white">{user.full_name || 'Not provided'}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">Email Address</Label>
                <div className="flex items-center gap-2 p-3 bg-agency-dark/50 rounded-md">
                  <Mail className="h-4 w-4 text-agency-white-muted" />
                  <span className="text-white">{user.email}</span>
                  <Badge variant="outline" className="ml-auto text-xs">Verified</Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="text-white">Phone Number</Label>
                {isEditing ? (
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="bg-agency-dark border-agency-green/20 text-white"
                    placeholder="+234 xxx xxx xxxx"
                  />
                ) : (
                  <div className="flex items-center gap-2 p-3 bg-agency-dark/50 rounded-md">
                    <Phone className="h-4 w-4 text-agency-white-muted" />
                    <span className="text-white">{user.phone || 'Not provided'}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-white">Account Type</Label>
                <div className="flex items-center gap-2 p-3 bg-agency-dark/50 rounded-md">
                  <Shield className="h-4 w-4 text-agency-white-muted" />
                  <span className="text-white">{user.subscription_type || 'Standard'}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Account Actions */}
      <Card className="profile-card bg-agency-darker/50 border-agency-green/10">
        <CardHeader>
          <CardTitle className="text-white">Account Actions</CardTitle>
          <CardDescription className="text-agency-white-muted">
            Manage your account settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start">
              <Shield className="mr-2 h-4 w-4" />
              Change Password
            </Button>
            <Button variant="outline" className="justify-start">
              <Mail className="mr-2 h-4 w-4" />
              Email Preferences
            </Button>
            <Button variant="outline" className="justify-start text-red-400 border-red-400/20 hover:bg-red-500/10">
              <X className="mr-2 h-4 w-4" />
              Delete Account
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ProfilePage
