import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl =
  import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey =
  import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database table names
export const TABLES = {
  USERS: 'users',
  PROJECTS: 'projects',
  PAYMENTS: 'payments',
  MESSAGES: 'messages',
  DOCUMENTS: 'documents',
  PROJECT_MILESTONES: 'project_milestones'
} as const

// Database types
export interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  created_at: string
  updated_at: string
  payment_status: 'pending' | 'paid' | 'expired'
  subscription_type?: string
}

export interface Project {
  id: string
  user_id: string
  name: string
  description: string
  package_type: string
  status: 'planning' | 'in_progress' | 'review' | 'completed' | 'on_hold'
  progress_percentage: number
  start_date: string
  estimated_completion: string
  actual_completion?: string
  created_at: string
  updated_at: string
}

export interface Payment {
  id: string
  user_id: string
  project_id?: string
  amount: number
  currency: string
  payment_method: string
  paystack_reference: string
  status: 'pending' | 'success' | 'failed'
  payment_date: string
  created_at: string
}

export interface Message {
  id: string
  user_id: string
  project_id?: string
  sender_type: 'client' | 'agency'
  sender_name: string
  subject: string
  content: string
  is_read: boolean
  created_at: string
  updated_at: string
}

export interface Document {
  id: string
  user_id: string
  project_id?: string
  name: string
  type: 'contract' | 'invoice' | 'deliverable' | 'other'
  file_url: string
  file_size: number
  mime_type: string
  uploaded_by: string
  created_at: string
}

export interface ProjectMilestone {
  id: string
  project_id: string
  title: string
  description: string
  status: 'pending' | 'in_progress' | 'completed'
  due_date: string
  completed_date?: string
  order_index: number
  created_at: string
  updated_at: string
}

// Helper functions for database operations
export const dbHelpers = {
  // User operations
  async getUserById(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single()

    if (error) throw error
    return data as User
  },

  // Project operations
  async getUserProjects(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.PROJECTS)
      .select(
        `
        *,
        project_milestones (*)
      `
      )
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as (Project & { project_milestones: ProjectMilestone[] })[]
  },

  // Payment operations
  async getUserPayments(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.PAYMENTS)
      .select('*')
      .eq('user_id', userId)
      .order('payment_date', { ascending: false })

    if (error) throw error
    return data as Payment[]
  },

  // Message operations
  async getUserMessages(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.MESSAGES)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as Message[]
  },

  // Document operations
  async getUserDocuments(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.DOCUMENTS)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as Document[]
  },

  // Verify payment status with Paystack
  async verifyPaymentStatus(userId: string): Promise<boolean> {
    try {
      const payments = await this.getUserPayments(userId)
      return payments.some((payment) => payment.status === 'success')
    } catch (error) {
      console.error('Error verifying payment status:', error)
      return false
    }
  }
}
