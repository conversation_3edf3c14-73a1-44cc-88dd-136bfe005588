import React, { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Check } from 'lucide-react'
import { packages, Package, SALE_END_DATE } from '@/data/pricing-packages'

export default function PricingSection() {
  const [now, setNow] = useState(Date.now())
  const [monthlySubscriptions, setMonthlySubscriptions] = useState<{
    [key: number]: boolean
  }>({})

  useEffect(() => {
    const interval = setInterval(() => setNow(Date.now()), 1000)
    return () => clearInterval(interval)
  }, [])

  const timeLeft = SALE_END_DATE - now
  const expired = timeLeft <= 0

  const countdown = expired
    ? null
    : `${Math.floor(timeLeft / (1000 * 60 * 60 * 24))}d ${Math.floor(
        (timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )}h ${Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))}m`

  const handleCheckboxChange = (packageIndex: number, checked: boolean) => {
    setMonthlySubscriptions((prev) => ({
      ...prev,
      [packageIndex]: checked
    }))
  }

  const handlePayment = (pkg: Package, packageIndex: number) => {
    const isMonthly = monthlySubscriptions[packageIndex] || false
    const paymentUrl =
      isMonthly && pkg.monthlyPaymentUrl
        ? pkg.monthlyPaymentUrl
        : pkg.oneTimePaymentUrl

    window.open(paymentUrl, '_blank')
  }

  return (
    <>
      <section className="section-padding bg-agency-dark relative overflow-hidden">
        <div className="container mx-auto my-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {packages.map((pkg, idx) => (
              <Card
                key={idx}
                className={`relative rounded-xl shadow-lg border transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 bg-agency-darker border-agency-white-muted/20 ${
                  pkg.highlight ? 'ring-2 ring-agency-green scale-105' : ''
                }`}
              >
                {pkg.highlight && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-agency-green text-agency-dark text-xs font-semibold px-4 py-2 rounded-full shadow-lg">
                    Most Popular
                  </div>
                )}

                {pkg.new && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-agency-green text-agency-dark text-xs font-semibold px-4 py-2 rounded-full shadow-lg">
                    New Service
                  </div>
                )}

                <CardContent className="p-8 flex flex-col h-full">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {pkg.name}
                    </h3>
                    <p className="text-agency-white-muted mb-6">
                      {pkg.subtitle}
                    </p>

                    <div className="mb-4">
                      {pkg.oldPrice && !expired && (
                        <p className="text-lg line-through text-agency-rose-muted  font-semibold">
                          {pkg.oldPrice}
                        </p>
                      )}
                      <p className="text-4xl font-extrabold text-agency-green">
                        {pkg.salePrice}
                      </p>
                      {pkg.renewal && (
                        <p className="text-sm text-agency-white-muted mt-2">
                          Annual Renewal: {pkg.renewal}
                        </p>
                      )}
                    </div>
                  </div>

                  <ul className="space-y-4 mb-8 flex-grow">
                    {pkg.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <Check className="w-5 h-5 text-agency-green mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-agency-white-muted">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  {/* {pkg.monthly && (
                    <div className="mb-6 p-4 bg-agency-dark rounded-lg border border-agency-white-muted/10">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          id={`monthly-${idx}`}
                          checked={monthlySubscriptions[idx] || false}
                          onCheckedChange={(checked) =>
                            handleCheckboxChange(idx, checked as boolean)
                          }
                          className="border-agency-green data-[state=checked]:bg-agency-green data-[state=checked]:border-agency-green"
                        />
                        <label
                          htmlFor={`monthly-${idx}`}
                          className="text-sm text-agency-white-muted cursor-pointer"
                        >
                          <span className="text-agency-green font-semibold">
                            Monthly subscription:
                          </span>{' '}
                          <span className="font-semibold">
                            {pkg.monthly}/month
                          </span>{' '}
                          for maintenance & updates
                        </label>
                      </div>
                    </div>
                  )} */}

                  <Button
                    className={`w-full py-6 text-lg font-semibold transition-all ${
                      pkg.highlight
                        ? 'bg-agency-green text-agency-dark hover:bg-agency-green/90 btn-glow'
                        : 'bg-transparent border-2 border-agency-green text-agency-green hover:bg-agency-green hover:text-agency-dark'
                    }`}
                    onClick={() => handlePayment(pkg, idx)}
                  >
                    {pkg.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </>
  )
}
