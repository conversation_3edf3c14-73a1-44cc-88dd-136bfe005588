import React, { useEffect } from 'react'
import { gsap } from 'gsap'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useProjects } from '@/hooks/useDashboard'
import { 
  FolderOpen, 
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  ExternalLink
} from 'lucide-react'

const ProjectsPage: React.FC = () => {
  const { data: projects, isLoading, error } = useProjects()

  useEffect(() => {
    if (!isLoading && projects) {
      gsap.fromTo('.project-card',
        { y: 30, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 0.6, 
          stagger: 0.1,
          ease: 'power2.out'
        }
      )
    }
  }, [isLoading, projects])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'on_hold':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      default:
        return <FolderOpen className="h-4 w-4 text-agency-white-muted" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: 'Completed', className: 'bg-green-500/10 text-green-500 border-green-500/20' },
      in_progress: { label: 'In Progress', className: 'bg-blue-500/10 text-blue-500 border-blue-500/20' },
      planning: { label: 'Planning', className: 'bg-purple-500/10 text-purple-500 border-purple-500/20' },
      review: { label: 'Under Review', className: 'bg-orange-500/10 text-orange-500 border-orange-500/20' },
      on_hold: { label: 'On Hold', className: 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.planning
    return <Badge className={config.className}>{config.label}</Badge>
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-agency-green mx-auto mb-4" />
          <p className="text-agency-white-muted">Loading your projects...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">Failed to load projects</p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Projects</h1>
          <p className="text-agency-white-muted mt-1">
            Track the progress of your projects and view important milestones
          </p>
        </div>
        <Button className="bg-agency-green hover:bg-agency-green/90 text-agency-dark">
          <ExternalLink className="mr-2 h-4 w-4" />
          Request New Project
        </Button>
      </div>

      {/* Projects Grid */}
      {projects && projects.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {projects.map((project) => (
            <Card key={project.id} className="project-card bg-agency-darker/50 border-agency-green/10 hover:border-agency-green/20 transition-all duration-300">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(project.status)}
                    <div>
                      <CardTitle className="text-white text-lg">{project.name}</CardTitle>
                      <CardDescription className="text-agency-white-muted">
                        {project.package_type}
                      </CardDescription>
                    </div>
                  </div>
                  {getStatusBadge(project.status)}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Description */}
                <p className="text-agency-white-muted text-sm">
                  {project.description || 'No description available'}
                </p>

                {/* Progress */}
                <div>
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-agency-white-muted">Progress</span>
                    <span className="text-white font-medium">{project.progress_percentage}%</span>
                  </div>
                  <Progress value={project.progress_percentage} className="h-2" />
                </div>

                {/* Milestones */}
                {project.milestones && project.milestones.length > 0 && (
                  <div>
                    <h4 className="text-white font-medium mb-2">Milestones</h4>
                    <div className="space-y-2">
                      {project.milestones.slice(0, 3).map((milestone) => (
                        <div key={milestone.id} className="flex items-center gap-2 text-sm">
                          {milestone.status === 'completed' ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : milestone.status === 'in_progress' ? (
                            <Clock className="h-4 w-4 text-blue-500" />
                          ) : (
                            <div className="h-4 w-4 rounded-full border-2 border-agency-white-muted" />
                          )}
                          <span className={milestone.status === 'completed' ? 'text-green-400' : 'text-agency-white-muted'}>
                            {milestone.title}
                          </span>
                        </div>
                      ))}
                      {project.milestones.length > 3 && (
                        <p className="text-xs text-agency-white-muted">
                          +{project.milestones.length - 3} more milestones
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Dates */}
                <div className="flex items-center justify-between text-xs text-agency-white-muted pt-2 border-t border-agency-green/10">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>Started: {new Date(project.start_date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>Due: {new Date(project.estimated_completion).toLocaleDateString()}</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    View Details
                  </Button>
                  <Button variant="ghost" size="sm" className="text-agency-green hover:bg-agency-green/10">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="bg-agency-darker/50 border-agency-green/10">
          <CardContent className="text-center py-12">
            <FolderOpen className="h-12 w-12 text-agency-white-muted mx-auto mb-4" />
            <h3 className="text-white text-lg font-medium mb-2">No Projects Yet</h3>
            <p className="text-agency-white-muted mb-6">
              You don't have any projects yet. Contact us to get started with your first project!
            </p>
            <Button className="bg-agency-green hover:bg-agency-green/90 text-agency-dark">
              Start Your First Project
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default ProjectsPage
