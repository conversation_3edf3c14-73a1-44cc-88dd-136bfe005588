import { User, Project, Payment, Message, Document, ProjectMilestone } from '@/lib/supabase'

// Dashboard-specific types
export interface DashboardStats {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  totalSpent: number
  pendingMessages: number
  recentDocuments: number
}

export interface ProjectWithMilestones extends Project {
  milestones: ProjectMilestone[]
  nextMilestone?: ProjectMilestone
  completedMilestones: number
  totalMilestones: number
}

export interface MessageThread {
  id: string
  subject: string
  lastMessage: Message
  messageCount: number
  unreadCount: number
  participants: string[]
  projectId?: string
  projectName?: string
}

export interface DocumentCategory {
  type: Document['type']
  label: string
  count: number
  documents: Document[]
}

export interface PaymentSummary {
  totalPaid: number
  totalPending: number
  lastPayment?: Payment
  paymentHistory: Payment[]
  subscriptionStatus?: 'active' | 'expired' | 'cancelled'
}

export interface DashboardData {
  user: User
  stats: DashboardStats
  projects: ProjectWithMilestones[]
  messages: MessageThread[]
  documents: DocumentCategory[]
  payments: PaymentSummary
}

// Navigation types
export interface DashboardNavItem {
  id: string
  label: string
  icon: string
  path: string
  badge?: number
}

export interface DashboardSection {
  id: string
  title: string
  description?: string
  component: React.ComponentType<any>
}

// Activity types
export interface ActivityItem {
  id: string
  type: 'project_update' | 'message' | 'document' | 'payment' | 'milestone'
  title: string
  description: string
  timestamp: string
  projectId?: string
  projectName?: string
  status?: 'info' | 'success' | 'warning' | 'error'
}

// Notification types
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  actionUrl?: string
  actionLabel?: string
}

// Filter and sort types
export interface ProjectFilter {
  status?: Project['status'][]
  packageType?: string[]
  dateRange?: {
    start: string
    end: string
  }
}

export interface MessageFilter {
  unreadOnly?: boolean
  projectId?: string
  dateRange?: {
    start: string
    end: string
  }
}

export interface DocumentFilter {
  type?: Document['type'][]
  projectId?: string
  dateRange?: {
    start: string
    end: string
  }
}

export interface SortOption {
  field: string
  direction: 'asc' | 'desc'
  label: string
}

// Form types
export interface MessageForm {
  subject: string
  content: string
  projectId?: string
}

export interface DocumentUpload {
  file: File
  name: string
  type: Document['type']
  projectId?: string
}

// Dashboard preferences
export interface DashboardPreferences {
  theme: 'light' | 'dark' | 'system'
  notifications: {
    email: boolean
    push: boolean
    projectUpdates: boolean
    messages: boolean
    payments: boolean
  }
  layout: {
    sidebarCollapsed: boolean
    compactMode: boolean
  }
}

// API response types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Loading states
export interface LoadingStates {
  dashboard: boolean
  projects: boolean
  messages: boolean
  documents: boolean
  payments: boolean
  profile: boolean
}

// Error types
export interface DashboardError {
  code: string
  message: string
  details?: any
}

// Component props types
export interface DashboardLayoutProps {
  children: React.ReactNode
}

export interface DashboardSidebarProps {
  collapsed: boolean
  onToggle: () => void
  navItems: DashboardNavItem[]
}

export interface ProjectCardProps {
  project: ProjectWithMilestones
  onViewDetails: (projectId: string) => void
}

export interface MessageListProps {
  messages: MessageThread[]
  onSelectMessage: (messageId: string) => void
  selectedMessageId?: string
}

export interface DocumentListProps {
  documents: Document[]
  onDownload: (documentId: string) => void
  onPreview: (documentId: string) => void
}

export interface PaymentHistoryProps {
  payments: Payment[]
  summary: PaymentSummary
}

// Utility types
export type DashboardView = 'overview' | 'projects' | 'messages' | 'documents' | 'payments' | 'profile'

export type ProjectStatus = Project['status']
export type MessageSender = Message['sender_type']
export type DocumentType = Document['type']
export type PaymentStatus = Payment['status']
